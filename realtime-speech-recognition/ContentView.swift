//
//  ContentView.swift
//  realtime-speech-recognition
//
//  Created by <PERSON><PERSON><PERSON> on 13/8/2025.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var speechManager = SpeechRecognitionManager()
    @State private var showingExport = false
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background with glass material
                Color.clear
                    .background(.regularMaterial, in: Rectangle())
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Header with controls
                    headerView
                    
                    // Main content area with two sections
                    mainContentView
                }
            }
        }
        .frame(minWidth: 1000, minHeight: 700)
        .background(.windowBackground)
        .sheet(isPresented: $showingExport) {
            ExportView(transcription: speechManager.exportTranscription())
        }
        .alert("Error", isPresented: .constant(speechManager.errorMessage != nil)) {
            Button("OK") {
                speechManager.errorMessage = nil
            }
        } message: {
            Text(speechManager.errorMessage ?? "")
        }
        .onAppear {
            // Request permissions on app launch
            speechManager.requestPermissions()
        }
    }
    
    private var headerView: some View {
        VStack(spacing: 0) {
            HStack(spacing: 16) {
                // App icon and title
                HStack(spacing: 12) {
                    Image(systemName: "waveform.badge.mic")
                        .font(.title2)
                        .foregroundStyle(.tint)
                    
                    Text("Speech Recognition")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundStyle(.primary)
                }
                
                Spacer()
                
                // Status indicator
                HStack(spacing: 8) {
                    Circle()
                        .fill(speechManager.isRecognizing ? .green : .secondary)
                        .frame(width: 8, height: 8)
                        .symbolEffect(.pulse, isActive: speechManager.isRecognizing)
                    
                    Text(speechManager.isRecognizing ? "Listening" : "Ready")
                        .font(.subheadline)
                        .foregroundStyle(.secondary)
                }
                
                // Control buttons
                HStack(spacing: 12) {
                    Button(action: speechManager.clearHistory) {
                        Label("Clear", systemImage: "trash")
                            .labelStyle(.iconOnly)
                    }
                    .buttonStyle(.borderless)
                    .disabled(speechManager.recognizedSentences.isEmpty && speechManager.transcribedText.isEmpty)
                    
                    Button(action: { showingExport = true }) {
                        Label("Export", systemImage: "square.and.arrow.up")
                            .labelStyle(.iconOnly)
                    }
                    .buttonStyle(.borderless)
                    .disabled(speechManager.recognizedSentences.isEmpty && speechManager.transcribedText.isEmpty)
                    
                    // Permission warning
                    if !speechManager.isAuthorized {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundStyle(.orange)
                            .font(.caption)
                    }
                }
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 16)
            .background(.thickMaterial, in: Rectangle())
        }
    }
    
    private var mainContentView: some View {
        VStack(spacing: 1) {
            // Historical sentences section (upper area)
            historyView
            
            // Current transcription section (lower area)
            currentTranscriptionView
            
            // Control panel at bottom
            controlPanelView
        }
    }
    
    private var historyView: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Section header
            HStack {
                Text("Previous Sentences")
                    .font(.headline)
                    .foregroundStyle(.secondary)
                
                Spacer()
                
                Text("\(speechManager.recognizedSentences.count) sentences")
                    .font(.caption)
                    .foregroundStyle(.tertiary)
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(.ultraThinMaterial, in: Rectangle())
            
            // History content
            ScrollView {
                LazyVStack(alignment: .leading, spacing: 12) {
                    if speechManager.recognizedSentences.isEmpty {
                        VStack(spacing: 16) {
                            Image(systemName: "text.bubble")
                                .font(.system(size: 32))
                                .foregroundStyle(.quaternary)
                            
                            Text("No previous sentences")
                                .font(.subheadline)
                                .foregroundStyle(.secondary)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.top, 40)
                    } else {
                        ForEach(Array(speechManager.recognizedSentences.enumerated().reversed()), id: \.offset) { index, sentence in
                            HStack(alignment: .top, spacing: 12) {
                                Text("\(speechManager.recognizedSentences.count - index)")
                                    .font(.caption2)
                                    .foregroundStyle(.quaternary)
                                    .frame(width: 24, alignment: .trailing)
                                    .padding(.top, 2)
                                
                                Text(sentence)
                                    .font(.body)
                                    .lineLimit(nil)
                                    .textSelection(.enabled)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                    .padding(.vertical, 12)
                                    .padding(.horizontal, 16)
                                    .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 12))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(.quaternary.opacity(0.3), lineWidth: 0.5)
                                    )
                            }
                        }
                    }
                }
                .padding(.horizontal, 24)
                .padding(.vertical, 16)
            }
            .frame(maxHeight: .infinity)
            .background(.thinMaterial, in: Rectangle())
        }
        .frame(maxHeight: .infinity)
    }
    
    private var currentTranscriptionView: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Section header
            HStack {
                Text("Current Recognition")
                    .font(.headline)
                    .foregroundStyle(.secondary)
                
                Spacer()
                
                if speechManager.isRecognizing {
                    HStack(spacing: 6) {
                        Image(systemName: "waveform")
                            .font(.caption)
                            .foregroundStyle(.blue)
                            .symbolEffect(.variableColor.iterative, isActive: true)
                        
                        Text("Live")
                            .font(.caption)
                            .foregroundStyle(.blue)
                    }
                }
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(.ultraThinMaterial, in: Rectangle())
            
            // Current transcription content
            VStack(alignment: .leading, spacing: 16) {
                if speechManager.transcribedText.isEmpty {
                    VStack(spacing: 24) {
                        Image(systemName: "mic.badge.plus")
                            .font(.system(size: 48))
                            .foregroundStyle(.quaternary)
                            .symbolEffect(.pulse, isActive: speechManager.isRecognizing)
                        
                        VStack(spacing: 8) {
                            Text(speechManager.isRecognizing ? "Listening..." : "Ready to transcribe")
                                .font(.title3)
                                .fontWeight(.medium)
                                .foregroundStyle(.secondary)
                            
                            Text(speechManager.isRecognizing ? "Speak clearly for best results" : "Press the microphone to start")
                                .font(.subheadline)
                                .foregroundStyle(.tertiary)
                                .multilineTextAlignment(.center)
                        }
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .padding(.top, 40)
                } else {
                    Text(speechManager.transcribedText)
                        .font(.title2)
                        .fontWeight(.medium)
                        .lineLimit(nil)
                        .textSelection(.enabled)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 20)
                        .background(.blue.opacity(0.08), in: RoundedRectangle(cornerRadius: 16))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(.blue.opacity(0.2), lineWidth: 1)
                        )
                        .animation(.easeInOut(duration: 0.2), value: speechManager.transcribedText)
                        .padding(.horizontal, 24)
                }
            }
            .frame(maxHeight: .infinity)
            .background(.thinMaterial, in: Rectangle())
        }
        .frame(height: 280)
    }
    
    private var controlPanelView: some View {
        HStack(spacing: 24) {
            // Main recording button
            Button(action: {
                if speechManager.isRecognizing {
                    speechManager.stopRecognition()
                } else {
                    speechManager.startRecognition()
                }
            }) {
                HStack(spacing: 12) {
                    Image(systemName: speechManager.isRecognizing ? "stop.circle.fill" : "mic.circle.fill")
                        .font(.title2)
                        .symbolEffect(.pulse, isActive: speechManager.isRecognizing)
                    
                    Text(speechManager.isRecognizing ? "Stop Recording" : "Start Recording")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .frame(minWidth: 180)
                .padding(.vertical, 16)
                .padding(.horizontal, 24)
                .background(speechManager.isRecognizing ? Color.red.gradient : Color.blue.gradient)
                .foregroundStyle(.white)
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .shadow(color: speechManager.isRecognizing ? Color.red.opacity(0.3) : Color.blue.opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .disabled(!speechManager.isAuthorized)
            .keyboardShortcut(.space, modifiers: [])
            .scaleEffect(speechManager.isRecognizing ? 1.02 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: speechManager.isRecognizing)
            
            Spacer()
            
            // Status and shortcuts
            VStack(alignment: .trailing, spacing: 4) {
                HStack(spacing: 8) {
                    Text("Total sentences:")
                    Text("\(speechManager.recognizedSentences.count)")
                        .fontWeight(.semibold)
                        .foregroundStyle(.blue)
                }
                .font(.caption)
                .foregroundStyle(.secondary)
                
                Text("Space to toggle • ⌘K to clear")
                    .font(.caption2)
                    .foregroundStyle(.tertiary)
            }
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 20)
        .background(.thickMaterial, in: Rectangle())
    }
}

struct ExportView: View {
    let transcription: String
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(alignment: .leading, spacing: 16) {
                Text("Export Transcription")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                ScrollView {
                    Text(transcription)
                        .font(.body)
                        .textSelection(.enabled)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding()
                        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 8))
                }
                
                HStack {
                    Button("Copy to Clipboard") {
                        NSPasteboard.general.clearContents()
                        NSPasteboard.general.setString(transcription, forType: .string)
                    }
                    .buttonStyle(.borderedProminent)
                    
                    Spacer()
                    
                    Button("Close") {
                        dismiss()
                    }
                }
            }
            .padding()
            .frame(width: 500, height: 400)
        }
    }
}

#Preview {
    ContentView()
}
