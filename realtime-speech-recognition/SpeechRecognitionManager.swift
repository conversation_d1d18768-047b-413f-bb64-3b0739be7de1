//
//  SpeechRecognitionManager.swift
//  realtime-speech-recognition
//
//  Created by <PERSON><PERSON><PERSON> on 13/8/2025.
//

import Foundation
import Speech
import AVFoundation
import WhisperKit

@MainActor
class SpeechRecognitionManager: NSObject, ObservableObject {
    @Published var transcribedText: String = ""
    @Published var recognizedSentences: [String] = []
    @Published var isRecognizing: Bool = false
    @Published var isAuthorized: Bool = false
    @Published var errorMessage: String?
    
    // Flag to track user-initiated stops
    private var isUserStoppedRecognition: Bool = false
    
    private var audioEngine = AVAudioEngine()
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var whisperKit: WhisperKit?
    
    // Audio buffer for WhisperKit processing
    private var audioBuffer: [Float] = []
    private let bufferSize = 16000 * 2 // 2 seconds at 16kHz
    
    override init() {
        super.init()
        setupSpeechRecognizer()
        setupWhisperKit()
        requestPermissions()
    }
    
    private func setupSpeechRecognizer() {
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "en-US"))
        speechRecognizer?.delegate = self
    }
    
    private func setupWhisperKit() {
        Task {
            do {
                // Initialize WhisperKit with a suitable model for real-time processing
                let config = WhisperKitConfig(model: "base", verbose: false)
                whisperKit = try await WhisperKit(config)
                print("WhisperKit initialized successfully")
            } catch {
                print("Failed to initialize WhisperKit: \(error)")
                errorMessage = "Failed to initialize speech recognition: \(error.localizedDescription)"
            }
        }
    }
    
    func requestPermissions() {
        // Request speech recognition permission
        SFSpeechRecognizer.requestAuthorization { [weak self] status in
            DispatchQueue.main.async {
                self?.isAuthorized = status == .authorized
                if !(self?.isAuthorized ?? false) {
                    self?.errorMessage = "Speech recognition permission denied"
                }
            }
        }
        
        // Request microphone permission (iOS only)
        #if os(iOS)
        AVAudioSession.sharedInstance().requestRecordPermission { granted in
            DispatchQueue.main.async {
                if !granted {
                    self.errorMessage = "Microphone permission denied"
                }
            }
        }
        #endif
        
        // On macOS, microphone permission is handled automatically by the system
        // when AVAudioEngine starts recording
    }
    
    func startRecognition() {
        guard isAuthorized else {
            errorMessage = "Please grant microphone and speech recognition permissions"
            return
        }
        
        guard !isRecognizing else { return }
        
        do {
            try startAudioEngine()
            isRecognizing = true
            errorMessage = nil
        } catch {
            errorMessage = "Failed to start recognition: \(error.localizedDescription)"
        }
    }
    
    func stopRecognition() {
        guard isRecognizing else { return }
        
        // Set flag to indicate this is a user-initiated stop
        isUserStoppedRecognition = true
        
        // Stop audio engine and remove tap
        audioEngine.stop()
        audioEngine.inputNode.removeTap(onBus: 0)
        
        // Clean up recognition components
        recognitionRequest?.endAudio()
        recognitionTask?.cancel()
        
        recognitionRequest = nil
        recognitionTask = nil
        isRecognizing = false
        
        // Save current transcribed text as a sentence
        if !transcribedText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            recognizedSentences.append(transcribedText)
            transcribedText = ""
        }
        
        // Clear the flag after a short delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.isUserStoppedRecognition = false
        }
    }
    
    private func startAudioEngine() throws {
        // Setup audio engine
        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)
        
        // Remove any existing tap first (safety measure)
        inputNode.removeTap(onBus: 0)
        
        // Create recognition request
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            throw NSError(domain: "SpeechRecognition", code: 1, userInfo: [NSLocalizedDescriptionKey: "Unable to create recognition request"])
        }
        
        recognitionRequest.shouldReportPartialResults = true
        
        // Install tap on audio input
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { [weak self] buffer, _ in
            self?.recognitionRequest?.append(buffer)
            self?.processAudioForWhisper(buffer: buffer)
        }
        
        // Start recognition task
        recognitionTask = speechRecognizer?.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            DispatchQueue.main.async {
                if let result = result {
                    self?.transcribedText = result.bestTranscription.formattedString
                    
                    if result.isFinal {
                        // When a sentence is complete, add it to recognized sentences
                        if !result.bestTranscription.formattedString.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                            self?.recognizedSentences.append(result.bestTranscription.formattedString)
                            self?.transcribedText = ""
                        }
                    }
                }
                
                if let error = error {
                    // Check if this is a user-initiated cancellation
                    if let nsError = error as NSError?,
                       nsError.domain == "kAFAssistantErrorDomain" && nsError.code == 216 ||
                       nsError.domain == "SpeechFramework" ||
                       error.localizedDescription.contains("canceled") ||
                       error.localizedDescription.contains("cancelled") {
                        
                        // If user stopped recognition, don't show error
                        if !(self?.isUserStoppedRecognition ?? false) {
                            print("Recognition was cancelled (not by user): \(error.localizedDescription)")
                        }
                    } else {
                        // This is a real error, show it to the user
                        self?.errorMessage = "Recognition error: \(error.localizedDescription)"
                    }
                    
                    // Only call stopRecognition if not already stopped by user
                    if !(self?.isUserStoppedRecognition ?? false) {
                        self?.stopRecognition()
                    }
                }
            }
        }
        
        // Start audio engine
        audioEngine.prepare()
        try audioEngine.start()
    }
    
    private func processAudioForWhisper(buffer: AVAudioPCMBuffer) {
        // Convert audio buffer to Float array for WhisperKit
        guard let channelData = buffer.floatChannelData?[0] else { return }
        let frames = Int(buffer.frameLength)
        
        let audioSamples = Array(UnsafeBufferPointer(start: channelData, count: frames))
        audioBuffer.append(contentsOf: audioSamples)
        
        // Keep buffer size manageable
        if audioBuffer.count > bufferSize {
            audioBuffer.removeFirst(audioBuffer.count - bufferSize)
        }
    }
    
    func clearHistory() {
        recognizedSentences.removeAll()
        transcribedText = ""
    }
    
    func exportTranscription() -> String {
        let allText = recognizedSentences.joined(separator: "\n")
        if !transcribedText.isEmpty {
            return allText + "\n" + transcribedText
        }
        return allText
    }
}

// MARK: - SFSpeechRecognizerDelegate
extension SpeechRecognitionManager: SFSpeechRecognizerDelegate {
    nonisolated func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
        DispatchQueue.main.async {
            if !available {
                self.errorMessage = "Speech recognition not available"
                self.stopRecognition()
            }
        }
    }
}
